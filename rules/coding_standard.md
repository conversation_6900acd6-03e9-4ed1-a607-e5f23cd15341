# General Coding Standards

This document outlines general coding standards applicable to any programming language, promoting consistency, readability, and maintainability across projects.

## 1. Naming Conventions

Consistent naming is crucial for code readability and understanding.

-   **Classes, Types, and Constructors:** Use PascalCase (e.g., `MyClass`, `UserService`).
-   **Functions, Methods, and Variables:** Use camelCase (e.g., `calculateTotal()`, `userName`).
-   **Constants:** Use UPPER_SNAKE_CASE (e.g., `MAX_RETRIES`, `DEFAULT_TIMEOUT`).
-   **Boolean Variables/Functions:** Prefix with `is`, `has`, `can`, or `should` (e.g., `isActive`, `hasPermission`).

## 2. Code Structure and Readability

Organize code logically to enhance understanding and maintainability.

-   **Indentation**: Use consistent indentation (e.g., 2 or 4 spaces).
-   **Line Length**: Aim for a reasonable maximum line length (e.g., 120 characters). Break long lines logically for readability.
-   **Clarity**: Write self-documenting code. Use meaningful names and avoid unnecessary comments.
-   **Modularity**: Break down complex logic into smaller, focused functions or modules.
-   **Conditional Logic**: Simplify complex conditional statements. Use well-named boolean variables to improve readability.

## 3. Code Reuse and Duplication Avoidance

Reduce code duplication to improve maintainability and reduce technical debt.

-   **DRY Principle (Don't Repeat Yourself)**: Refactor similar logic into reusable functions, classes, or modules.
-   **Identify Common Patterns**: Abstract repeated patterns into reusable components or services.
-   **Parameterization**: Use parameters to generalize functionality instead of copying and modifying code.
-   **Abstraction**: Utilize appropriate abstraction mechanisms (e.g., inheritance, composition, interfaces) to share common functionality.
-   **Code Reviews**: Actively look for and flag duplicate code during reviews.

## 4. Testing Standards

Ensure code quality and reliability through comprehensive testing.

-   **Test Coverage**: Aim for high test coverage, focusing on critical business logic.
-   **Test Naming**: Use clear and descriptive names for test classes and methods (e.g., `FeatureNameTests`, `functionName_Scenario_ExpectedResult`).
-   **Arrange-Act-Assert (AAA)**: Structure tests clearly into these three phases:
    -   **Arrange**: Set up test data and mocks.
    -   **Act**: Execute the code under test.
    -   **Assert**: Verify the outcome.
-   **Mocking**: Use mocking techniques to isolate units under test and manage dependencies. When the same mock data is used across multiple tests, extract it into a reusable method or a dedicated test utility.
-   **Edge Cases**: Write tests for edge cases, error conditions, and boundary values.
-   **Maintainability**: Keep tests clean, readable, and maintainable, just like production code.

## 5. Documentation

Maintain clear and up-to-date documentation.

-   **README**: Provide a comprehensive `README.md` file with project overview, setup instructions, and usage examples.
-   **Code Comments**: Use comments to explain *why* certain code exists, not *what* it does (unless the "what" is not obvious).
-   **API Documentation**: Document public APIs, functions, and modules.
-   **Remove Unused Imports/Dependencies**: Keep the codebase clean by removing unused imports, libraries, or dependencies.