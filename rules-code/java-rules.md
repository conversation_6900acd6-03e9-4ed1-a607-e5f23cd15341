# Common Payment Experience - Basic Coding Style Guide

This document outlines basic coding style guidelines for the Common Payment Experience project, ensuring consistency, readability, and maintainability.

## 1. Project Configuration Analysis

Before starting development on Java projects, analyze the project configuration to understand versions and dependencies for optimal coding practices.

-   **MCP Context7 for build.gradle**: Always use MCP context7 to read the `build.gradle` or `build.gradle.kts` file to understand:
    -   **Java Version**: Identify the target Java version (e.g., `sourceCompatibility = '17'`, `targetCompatibility = '17'`) to use appropriate language features and APIs.
    -   **Spring Boot Version**: Check the Spring Boot version in dependencies (e.g., `org.springframework.boot:spring-boot-starter:3.5.3`) to follow version-specific best practices.
    -   **Framework Dependencies**: Review other framework versions (Spring Security, Spring Data, etc.) for compatibility considerations.
    -   **Build Tool Configuration**: Understand Gradle/Maven configuration for proper dependency management.
-   **Version-Specific Best Practices**:
    -   Use Java 8+ features like Streams, Optional, and lambda expressions when available.
    -   Leverage Spring Boot auto-configuration and starter dependencies appropriately.
    -   Follow Spring Boot version-specific patterns for configuration and security.
-   **Performance Considerations**: Choose appropriate APIs and patterns based on the Java and framework versions in use.

## 2. Naming Conventions

-   **Classes and Interfaces:** PascalCase (e.g., `CommonPaymentController`, `DepositAccount`)
-   **Methods and Variables:** camelCase (e.g., `validateData()`, `getProcessorType()`, `isPayWithCreditCard`)
-   **Constants (Static Final Fields):** UPPER_SNAKE_CASE (e.g., `BILLER_CATEGORY_CODE_CREDIT_CARD`)

## 3. Utility Class Usage

Leverage existing utility classes for common operations to promote reusability and reduce boilerplate.

-   **`org.apache.commons.lang3.StringUtils`**: For null-safe string operations (e.g., `StringUtils.isNotBlank()`, `StringUtils.equals()`).
    ```java
    import org.apache.commons.lang3.StringUtils;
    if (StringUtils.isNotBlank(initializationCommonPaymentRequest.getPaymentInformation().getDeepLinkTransactionId())) {
        // Code logic here
    }
    ```
-   **`org.apache.commons.lang3.ObjectUtils`**: For null-safe object handling (e.g., `ObjectUtils.anyNull()`).
    ```java
    import org.apache.commons.lang3.ObjectUtils;
    if (ObjectUtils.anyNull(commonPaymentRule, commonPaymentRule.getDefaultPayment())) {
        // Code logic here
    }
    ```
-   **Custom `NullSafeUtils`**: For safe retrieval of nested properties with default values.
    ```java
    import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;
    boolean enableCallbackUrl = getSafeNullOrDefault(() -> initializationCommonPaymentRequest.getPaymentInformation().getCallback().isEnableCallbackFlag(), false);
    ```
-   **`org.springframework.util.CollectionUtils`**: For checking if collections are empty (e.g., `CollectionUtils.isEmpty()`).
    ```java
    import org.springframework.util.CollectionUtils;
    if (CollectionUtils.isEmpty(validChannels)) {
        // Code logic here
    }
    ```

## 4. Boolean Value Declaration and Usage

Ensure clarity and prevent common pitfalls.

-   **Primitive `boolean` vs. Wrapper `Boolean`**:
    -   Use primitive `boolean` for local variables, method parameters, and non-nullable fields.
    -   Use wrapper `Boolean` for DTOs/models where `null` is a valid state.
-   **Naming Boolean Variables/Fields**: Prefix with `is`, `has`, `can`, or `should` (e.g., `isActive`, `hasValidChannel`).
-   **Conditional Statements**: Directly use boolean variables in `if` statements. Avoid `== true` or `== false`.
    ```java
    // Good: if (isTransactionFromDeeplink) { /* ... */ }
    // Bad: if (isTransactionFromDeeplink == true) { /* ... */ }
    ```
    For `Boolean` wrapper types, use `Boolean.TRUE.equals()` or `Optional` for null handling.
    ```java
    // Nullable Boolean where null means false
    Boolean isMobileFromMasterBiller = cache.getValidateDraftCache().getMasterBillerResponse().getRef1().getIsMobile();
    if (Boolean.TRUE.equals(isMobileFromMasterBiller)) {
        // Code logic here
    }

    // Using Optional
    boolean isPayWithCreditCard = Optional.ofNullable(request.getCreditCard())
                                        .map(CreditCardValidationCommonPaymentRequest::isPayWithCreditCardFlag)
                                        .orElse(false);
    ```
-   **Negation**: Use the `!` operator (e.g., `!StringUtils.equalsAny(compCode, BILL_COMP_CODE_MEA, BILL_COMP_CODE_MWA)`).

## 5. Lombok Usage

Lombok annotations reduce boilerplate code.

-   **`@Data`**: Generates getters, setters, `equals()`, `hashCode()`, and `toString()`.
-   **`@Builder`**: Provides a fluent API for object creation.
-   **`@NoArgsConstructor` / `@AllArgsConstructor`**: Generates constructors.
-   **`@Accessors(chain = true)`**: Enables method chaining for setters.

## 6. General Style Guidelines

-   **Indentation**: Use 4 spaces.
-   **Brace Style**: Opening braces on the same line as the declaration.
    ```java
    public class MyClass {
        public void myMethod() {
            if (condition) {
                // Code logic here
            }
        }
    }
    ```
-   **Readability**: Break down complex conditional logic into smaller, well-named boolean variables.
-   **Maximum Line Length**: Aim for 120 characters. Break long lines after a comma, before an operator, or at higher-level expressions. Indent by 4 spaces.
    ```java
    // Good
    someObject.doSomething(param1, param2,
                          param3, param4);

    // Good
    if (condition1 && condition2 ||
        condition3 && condition4) {
        // Code logic here
    }
    ```

## 7. Import Statements

Proper import management contributes to cleaner and more efficient code.

-   **Remove Unused Imports**: Always remove unused import statements.
-   **Static Imports**: Use for frequently used static members (fields and methods) to improve readability.
    ```java
    import static com.tmb.oneapp.commonpaymentexp.utils.NullSafeUtils.getSafeNullOrDefault;
    import static org.mockito.ArgumentMatchers.anyBoolean;
    import static org.junit.jupiter.api.Assertions.assertTrue;
    ```

## 8. Code Reuse and Duplication Avoidance

Reducing code duplication is essential for maintainability and reducing technical debt.

-   **DRY Principle**: Avoid duplicating code. Refactor similar logic into reusable functions, utility classes, or components.
-   **Identify Common Patterns**: Abstract repeated patterns into reusable components or services.
-   **Parameterization**: Parameterize functionality instead of copying and modifying code.
-   **Utilize Inheritance and Composition**: Use OOP principles to share common functionality.
-   **Code Reviews**: Actively look for and flag duplicate code during reviews.
-   **Refactor When Possible**: Refactor duplicate code into shared utilities/services, even if not part of the current task.

## 9. Unit Testing Standards

When creating or editing code, ensure corresponding unit tests are written or updated to maintain code quality and reliability. The preferred testing framework is JUnit 5, and Mockito is the preferred mocking framework.

-   **Test Coverage**: Aim for high test coverage, focusing on critical business logic.
-   **Test Naming**: Use clear and descriptive names for test classes and methods (e.g., `ClassNameTest`, `methodName_Scenario_ExpectedResult`).
-   **Arrange-Act-Assert (AAA)**: Structure tests clearly into these three phases.
-   **Mocking**: Use mocking frameworks (e.g., Mockito) to isolate units under test and manage dependencies. When the same mock data is used across multiple tests, extract it into a reusable method or a dedicated test utility class to reduce duplication and improve maintainability.
-   **Edge Cases**: Write tests for edge cases, error conditions, and boundary values.
-   **Maintainability**: Keep tests clean, readable, and maintainable, just like production code.